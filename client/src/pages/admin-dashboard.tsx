import { useQuery } from "@tanstack/react-query";
import { Helmet } from "react-helmet";
import { Shield, Users, UserCheck, Settings, Activity } from "lucide-react";
import KeyMetrics from "@/components/dashboard/key-metrics";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function AdminDashboard() {
  const { data: adminData, isLoading } = useQuery({
    queryKey: ['/api/admin/dashboard'],
    refetchInterval: 60000,
  });

  return (
    <>
      <Helmet>
        <title>Admin Dashboard | Mentor Match</title>
        <meta name="description" content="Administrative overview and system controls" />
      </Helmet>

      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-semibold text-gray-800 flex items-center">
            <Shield className="mr-3 h-6 w-6 text-primary" />
            Admin Dashboard
          </h1>
          <p className="mt-1 text-sm text-gray-500">Administrative overview and system controls</p>
        </div>
      </div>

      {/* Reuse existing KeyMetrics component */}
      <KeyMetrics data={adminData} isLoading={isLoading} />

      {/* Admin-specific metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? "..." : (adminData?.adminSpecific?.totalUsers || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              All registered users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? "..." : (adminData?.adminSpecific?.pendingApprovals || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Mentors & mentees awaiting approval
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Operational</div>
            <p className="text-xs text-muted-foreground">
              All systems running normally
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Admin actions placeholder */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="mr-2 h-5 w-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-gray-500">
              <p>Admin quick actions will be available here.</p>
              <p className="text-sm mt-2">This is your dedicated admin workspace.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
